"use client"

import React, { useState } from 'react'
import GazeStabilityList from './gaze-stability-list'
import GazeStabilityDetail from './gaze-stability-detail'
import { GazeStabilityRecord } from '@/lib/api'

export default function GazeStabilityPage() {
  const [currentView, setCurrentView] = useState<'list' | 'detail'>('list')
  const [selectedRecord, setSelectedRecord] = useState<GazeStabilityRecord | null>(null)

  const handleViewDetail = (record: GazeStabilityRecord) => {
    setSelectedRecord(record)
    setCurrentView('detail')
  }

  const handleBackToList = () => {
    setSelectedRecord(null)
    setCurrentView('list')
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {currentView === 'list' ? (
        <GazeStabilityList onViewDetail={handleViewDetail} />
      ) : (
        selectedRecord && (
          <GazeStabilityDetail 
            recordId={selectedRecord.recordId} 
            onBack={handleBackToList} 
          />
        )
      )}
    </div>
  )
}
