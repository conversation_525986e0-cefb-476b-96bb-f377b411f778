"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar, Search, Eye, FileText, Clock, User, Activity } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import apiService, { GazeStabilityRecord, PageResponse, PageParams } from "@/lib/api"

interface GazeStabilityListProps {
  onViewDetail?: (record: GazeStabilityRecord) => void
}

export default function GazeStabilityList({ onViewDetail }: GazeStabilityListProps) {
  const [records, setRecords] = useState<GazeStabilityRecord[]>([])
  const [loading, setLoading] = useState(false)
  const [pagination, setPagination] = useState({
    current: 1,
    size: 10,
    total: 0,
    pages: 0
  })
  
  // 搜索参数
  const [searchParams, setSearchParams] = useState<PageParams>({
    current: 1,
    size: 10,
    patientName: '',
    status: '',
  })

  const { toast } = useToast()

  // 获取数据
  const fetchData = async (params: PageParams = searchParams) => {
    setLoading(true)
    try {
      // 使用示例API URL，实际使用时需要配置正确的baseURL
      const response = await apiService.getGazeStabilityList(params)
      setRecords(response.data.records)
      setPagination({
        current: response.data.current,
        size: response.data.size,
        total: response.data.total,
        pages: response.data.pages
      })
    } catch (error) {
      toast({
        title: "获取数据失败",
        description: error instanceof Error ? error.message : "请检查网络连接",
        variant: "destructive",
      })
      console.error('获取注视稳定性测试数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 初始化数据
  useEffect(() => {
    fetchData()
  }, [])

  // 搜索处理
  const handleSearch = () => {
    const newParams = { ...searchParams, current: 1 }
    setSearchParams(newParams)
    fetchData(newParams)
  }

  // 重置搜索
  const handleReset = () => {
    const resetParams = {
      current: 1,
      size: 10,
      patientName: '',
      status: '',
    }
    setSearchParams(resetParams)
    fetchData(resetParams)
  }

  // 分页处理
  const handlePageChange = (page: number) => {
    const newParams = { ...searchParams, current: page }
    setSearchParams(newParams)
    fetchData(newParams)
  }

  // 状态标签颜色
  const getStatusBadge = (status: string, statusDesc: string) => {
    const colorMap: Record<string, string> = {
      'COMPLETED': 'bg-green-100 text-green-800',
      'IN_PROGRESS': 'bg-blue-100 text-blue-800',
      'FAILED': 'bg-red-100 text-red-800',
      'PENDING': 'bg-yellow-100 text-yellow-800',
    }
    
    return (
      <Badge className={colorMap[status] || 'bg-gray-100 text-gray-800'}>
        {statusDesc}
      </Badge>
    )
  }

  // 格式化时间
  const formatDateTime = (dateTime: string) => {
    return new Date(dateTime).toLocaleString('zh-CN')
  }

  // 格式化持续时间
  const formatDuration = (duration: number) => {
    const seconds = Math.floor(duration / 1000)
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return minutes > 0 ? `${minutes}分${remainingSeconds}秒` : `${remainingSeconds}秒`
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和统计 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">注视稳定性测试</h1>
          <p className="text-muted-foreground">
            管理和查看患者的注视稳定性测试记录
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <Card className="px-4 py-2">
            <div className="flex items-center space-x-2">
              <Activity className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm font-medium">总测试数</p>
                <p className="text-2xl font-bold text-blue-600">{pagination.total}</p>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Search className="h-5 w-5" />
            <span>搜索筛选</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">患者姓名</label>
              <Input
                placeholder="输入患者姓名"
                value={searchParams.patientName || ''}
                onChange={(e) => setSearchParams(prev => ({ ...prev, patientName: e.target.value }))}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">测试状态</label>
              <Select
                value={searchParams.status || ''}
                onValueChange={(value) => setSearchParams(prev => ({ ...prev, status: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部状态</SelectItem>
                  <SelectItem value="COMPLETED">已完成</SelectItem>
                  <SelectItem value="IN_PROGRESS">进行中</SelectItem>
                  <SelectItem value="FAILED">失败</SelectItem>
                  <SelectItem value="PENDING">待处理</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end space-x-2">
              <Button onClick={handleSearch} disabled={loading}>
                <Search className="h-4 w-4 mr-2" />
                搜索
              </Button>
              <Button variant="outline" onClick={handleReset} disabled={loading}>
                重置
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 数据表格 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>测试记录</span>
          </CardTitle>
          <CardDescription>
            共 {pagination.total} 条记录，第 {pagination.current} 页，共 {pagination.pages} 页
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>患者信息</TableHead>
                  <TableHead>测试信息</TableHead>
                  <TableHead>设备信息</TableHead>
                  <TableHead>测试时间</TableHead>
                  <TableHead>持续时间</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <div className="flex items-center justify-center space-x-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                        <span>加载中...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : records.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                      暂无数据
                    </TableCell>
                  </TableRow>
                ) : (
                  records.map((record) => (
                    <TableRow key={record.id} className="hover:bg-muted/50">
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2">
                            <User className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">{record.patientName}</span>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            住院号: {record.inpatientNum}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            病历号: {record.caseCardNum}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">{record.testType}</div>
                          <div className="text-sm text-muted-foreground">
                            序列: {record.testSequence}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            记录ID: {record.recordId}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="text-sm">
                            设备ID: {record.deviceId}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            SN: {record.deviceSn}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{formatDateTime(record.testDate)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{formatDuration(record.duration)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(record.status, record.statusDesc)}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onViewDetail?.(record)}
                          className="flex items-center space-x-1"
                        >
                          <Eye className="h-4 w-4" />
                          <span>查看详情</span>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* 分页 */}
          {pagination.pages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                显示第 {(pagination.current - 1) * pagination.size + 1} 到{' '}
                {Math.min(pagination.current * pagination.size, pagination.total)} 条，
                共 {pagination.total} 条记录
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.current - 1)}
                  disabled={pagination.current <= 1 || loading}
                >
                  上一页
                </Button>
                <span className="text-sm">
                  第 {pagination.current} 页，共 {pagination.pages} 页
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.current + 1)}
                  disabled={pagination.current >= pagination.pages || loading}
                >
                  下一页
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
