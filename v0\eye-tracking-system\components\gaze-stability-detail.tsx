"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { 
  ArrowLeft, 
  User, 
  Calendar, 
  Clock, 
  Activity, 
  Target, 
  Eye, 
  Settings,
  FileText,
  BarChart3
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import apiService, { GazeStabilityRecord, GazePoint } from "@/lib/api"

interface GazeStabilityDetailProps {
  recordId: number
  onBack?: () => void
}

export default function GazeStabilityDetail({ recordId, onBack }: GazeStabilityDetailProps) {
  const [record, setRecord] = useState<GazeStabilityRecord | null>(null)
  const [gazePoints, setGazePoints] = useState<GazePoint[]>([])
  const [loading, setLoading] = useState(false)
  
  const { toast } = useToast()

  // 获取详情数据
  const fetchDetail = async () => {
    setLoading(true)
    try {
      const response = await apiService.getGazeStabilityDetail(recordId)
      setRecord(response.data)
      
      // 解析注视轨迹数据
      if (response.data.gazeTrajectoryJson) {
        try {
          const points = JSON.parse(response.data.gazeTrajectoryJson)
          // 确保解析的数据是数组
          if (Array.isArray(points)) {
            setGazePoints(points as GazePoint[])
          } else {
            console.error('注视轨迹数据不是数组格式:', points)
            setGazePoints([])
          }
        } catch (error) {
          console.error('解析注视轨迹数据失败:', error)
          setGazePoints([])
        }
      } else {
        setGazePoints([])
      }
    } catch (error) {
      toast({
        title: "获取详情失败",
        description: error instanceof Error ? error.message : "请检查网络连接",
        variant: "destructive",
      })
      console.error('获取注视稳定性测试详情失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDetail()
  }, [recordId])

  // 状态标签颜色
  const getStatusBadge = (status: string, statusDesc: string) => {
    const colorMap: Record<string, string> = {
      'COMPLETED': 'bg-green-100 text-green-800',
      'IN_PROGRESS': 'bg-blue-100 text-blue-800',
      'FAILED': 'bg-red-100 text-red-800',
      'PENDING': 'bg-yellow-100 text-yellow-800',
    }
    
    return (
      <Badge className={colorMap[status] || 'bg-gray-100 text-gray-800'}>
        {statusDesc}
      </Badge>
    )
  }

  // 格式化时间
  const formatDateTime = (dateTime: string) => {
    return new Date(dateTime).toLocaleString('zh-CN')
  }

  // 格式化持续时间
  const formatDuration = (duration: number) => {
    const seconds = Math.floor(duration / 1000)
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return minutes > 0 ? `${minutes}分${remainingSeconds}秒` : `${remainingSeconds}秒`
  }

  // 计算统计数据
  const getStatistics = () => {
    // 确保gazePoints是数组且不为空
    if (!Array.isArray(gazePoints) || gazePoints.length === 0) {
      return null
    }

    try {
      const totalPoints = gazePoints.length
      const inTargetCount = gazePoints.filter(p => p && p.isInTargetArea).length
      const validPoints = gazePoints.filter(p => p && typeof p.relativeDistance === 'number')

      if (validPoints.length === 0) {
        return null
      }

      const avgDistance = validPoints.reduce((sum, p) => sum + p.relativeDistance, 0) / validPoints.length
      const distances = validPoints.map(p => p.relativeDistance)
      const maxDistance = Math.max(...distances)
      const minDistance = Math.min(...distances)

      return {
        totalPoints,
        inTargetCount,
        inTargetRate: (inTargetCount / totalPoints * 100).toFixed(1),
        avgDistance: avgDistance.toFixed(3),
        maxDistance: maxDistance.toFixed(3),
        minDistance: minDistance.toFixed(3)
      }
    } catch (error) {
      console.error('计算统计数据时出错:', error)
      return null
    }
  }

  const statistics = getStatistics()

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          <span>加载中...</span>
        </div>
      </div>
    )
  }

  if (!record) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">未找到测试记录</p>
        <Button variant="outline" onClick={onBack} className="mt-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          返回列表
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回列表
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">注视稳定性测试详情</h1>
            <p className="text-muted-foreground">
              记录ID: {record.recordId} | 测试序列: {record.testSequence}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {getStatusBadge(record.status, record.statusDesc)}
        </div>
      </div>

      {/* 基本信息 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">患者信息</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold">{record.patientName}</div>
              <div className="text-sm text-muted-foreground">
                住院号: {record.inpatientNum}
              </div>
              <div className="text-sm text-muted-foreground">
                病历号: {record.caseCardNum}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">测试时间</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold">{formatDateTime(record.testDate)}</div>
              <div className="text-sm text-muted-foreground flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                持续时间: {formatDuration(record.duration)}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">设备信息</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold">设备 {record.deviceId}</div>
              <div className="text-sm text-muted-foreground">
                SN: {record.deviceSn}
              </div>
              {record.deviceName && (
                <div className="text-sm text-muted-foreground">
                  名称: {record.deviceName}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 详细信息标签页 */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="trajectory">轨迹数据</TabsTrigger>
          <TabsTrigger value="analysis">分析结果</TabsTrigger>
          <TabsTrigger value="settings">测试设置</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 目标点信息 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="h-5 w-5" />
                  <span>目标点信息</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">X坐标</label>
                    <p className="text-lg font-semibold">{record.targetX.toFixed(6)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Y坐标</label>
                    <p className="text-lg font-semibold">{record.targetY.toFixed(6)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">目标点半径</label>
                    <p className="text-lg font-semibold">{record.targetPointRadius}px</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">注视点半径</label>
                    <p className="text-lg font-semibold">{record.gazePointRadius}px</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 统计信息 */}
            {statistics && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <BarChart3 className="h-5 w-5" />
                    <span>统计信息</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">总点数</label>
                      <p className="text-lg font-semibold">{statistics.totalPoints}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">目标区域内</label>
                      <p className="text-lg font-semibold">{statistics.inTargetCount}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">命中率</label>
                      <p className="text-lg font-semibold">{statistics.inTargetRate}%</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">平均距离</label>
                      <p className="text-lg font-semibold">{statistics.avgDistance}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* 备注信息 */}
          {record.notes && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="h-5 w-5" />
                  <span>备注信息</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm">{record.notes}</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="trajectory" className="space-y-4">
          {/* 轨迹可视化 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Target className="h-5 w-5" />
                <span>注视轨迹可视化</span>
              </CardTitle>
              <CardDescription>
                红色圆圈为目标点，蓝色点为注视点轨迹
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="w-full h-96 border rounded-lg bg-gray-50 relative overflow-hidden">
                <svg width="100%" height="100%" viewBox="0 0 800 600" className="absolute inset-0">
                  {/* 目标点 */}
                  {record && (
                    <circle
                      cx={record.targetX * 800}
                      cy={record.targetY * 600}
                      r={record.targetPointRadius}
                      fill="none"
                      stroke="red"
                      strokeWidth="2"
                      opacity="0.7"
                    />
                  )}

                  {/* 注视轨迹点 */}
                  {Array.isArray(gazePoints) && gazePoints.map((point, index) => {
                    // 确保点数据有效
                    if (!point || typeof point.x !== 'number' || typeof point.y !== 'number') {
                      return null
                    }

                    return (
                      <g key={index}>
                        <circle
                          cx={point.x * 800}
                          cy={point.y * 600}
                          r="3"
                          fill={point.isInTargetArea ? "green" : "blue"}
                          opacity="0.6"
                        />
                        {/* 连接线 */}
                        {index > 0 && gazePoints[index - 1] &&
                         typeof gazePoints[index - 1].x === 'number' &&
                         typeof gazePoints[index - 1].y === 'number' && (
                          <line
                            x1={gazePoints[index - 1].x * 800}
                            y1={gazePoints[index - 1].y * 600}
                            x2={point.x * 800}
                            y2={point.y * 600}
                            stroke="gray"
                            strokeWidth="1"
                            opacity="0.3"
                          />
                        )}
                      </g>
                    )
                  })}

                  {/* 图例 */}
                  <g transform="translate(20, 20)">
                    <rect x="0" y="0" width="200" height="80" fill="white" stroke="gray" strokeWidth="1" opacity="0.9" />
                    <circle cx="15" cy="20" r="3" fill="red" />
                    <text x="25" y="25" fontSize="12" fill="black">目标点</text>
                    <circle cx="15" cy="40" r="3" fill="green" />
                    <text x="25" y="45" fontSize="12" fill="black">目标区域内</text>
                    <circle cx="15" cy="60" r="3" fill="blue" />
                    <text x="25" y="65" fontSize="12" fill="black">目标区域外</text>
                  </g>
                </svg>
              </div>
            </CardContent>
          </Card>

          {/* 轨迹数据表格 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Eye className="h-5 w-5" />
                <span>注视轨迹数据</span>
              </CardTitle>
              <CardDescription>
                共 {Array.isArray(gazePoints) ? gazePoints.length : 0} 个数据点
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="max-h-96 overflow-y-auto">
                <div className="grid grid-cols-1 gap-2">
                  {Array.isArray(gazePoints) && gazePoints.length > 0 ? (
                    gazePoints.map((point, index) => {
                      // 确保点数据有效
                      if (!point) return null

                      return (
                        <div key={index} className="flex items-center justify-between p-2 border rounded-lg">
                          <div className="flex items-center space-x-4">
                            <span className="text-sm font-medium">#{point.index || index + 1}</span>
                            <span className="text-sm">
                              ({typeof point.x === 'number' ? point.x.toFixed(3) : 'N/A'}, {typeof point.y === 'number' ? point.y.toFixed(3) : 'N/A'})
                            </span>
                            <Badge variant={point.isInTargetArea ? "default" : "secondary"}>
                              {point.isInTargetArea ? "目标内" : "目标外"}
                            </Badge>
                          </div>
                          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                            <span>距离: {typeof point.relativeDistance === 'number' ? point.relativeDistance.toFixed(3) : 'N/A'}</span>
                            <span>方向: {point.direction || 'N/A'}</span>
                            <span>环级: {point.ringLevel || 'N/A'}</span>
                          </div>
                        </div>
                      )
                    })
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      暂无轨迹数据
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analysis" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="h-5 w-5" />
                <span>分析结果</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">稳定性评分</label>
                  <p className="text-lg font-semibold">
                    {record.stabilityScore !== null ? record.stabilityScore : '未计算'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">评估结果</label>
                  <p className="text-lg font-semibold">
                    {record.evaluationResult || '未评估'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">X轴偏差</label>
                  <p className="text-lg font-semibold">
                    {record.deviationX !== null ? record.deviationX.toFixed(6) : '未计算'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Y轴偏差</label>
                  <p className="text-lg font-semibold">
                    {record.deviationY !== null ? record.deviationY.toFixed(6) : '未计算'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">注视持续时间</label>
                  <p className="text-lg font-semibold">
                    {record.fixationDuration !== null ? `${record.fixationDuration}ms` : '未计算'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">注视次数</label>
                  <p className="text-lg font-semibold">
                    {record.fixationCount !== null ? record.fixationCount : '未计算'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">平均距离</label>
                  <p className="text-lg font-semibold">
                    {record.averageDistance !== null ? record.averageDistance.toFixed(6) : '未计算'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">最大偏差</label>
                  <p className="text-lg font-semibold">
                    {record.maxDeviation !== null ? record.maxDeviation.toFixed(6) : '未计算'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">RMS误差</label>
                  <p className="text-lg font-semibold">
                    {record.rmsError !== null ? record.rmsError.toFixed(6) : '未计算'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>测试参数</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">环路半径增量</label>
                  <p className="text-lg font-semibold">{record.loopRadiusIncreases}px</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">环路数量</label>
                  <p className="text-lg font-semibold">{record.loopsCount}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">校准参数</label>
                  <p className="text-sm bg-muted p-2 rounded">
                    {record.calibrationParams}
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>环境信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">环境描述</label>
                  <p className="text-sm bg-muted p-2 rounded">
                    {record.environmentInfo}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
