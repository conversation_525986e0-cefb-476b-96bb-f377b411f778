"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/gaze-stability/page",{

/***/ "(app-pages-browser)/./components/gaze-stability-detail.tsx":
/*!**********************************************!*\
  !*** ./components/gaze-stability-detail.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GazeStabilityDetail)\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Calendar,Clock,Eye,FileText,Settings,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Calendar,Clock,Eye,FileText,Settings,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Calendar,Clock,Eye,FileText,Settings,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Calendar,Clock,Eye,FileText,Settings,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Calendar,Clock,Eye,FileText,Settings,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Calendar,Clock,Eye,FileText,Settings,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Calendar,Clock,Eye,FileText,Settings,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Calendar,Clock,Eye,FileText,Settings,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Calendar,Clock,Eye,FileText,Settings,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Calendar,Clock,Eye,FileText,Settings,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction GazeStabilityDetail(param) {\n    var _this = this;\n    var recordId = param.recordId, onBack = param.onBack;\n    _s();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_8__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), record = _useState[0], setRecord = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_8__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]), 2), gazePoints = _useState1[0], setGazePoints = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_8__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), loading = _useState2[0], setLoading = _useState2[1];\n    var toast = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)().toast;\n    // 获取详情数据\n    var fetchDetail = /*#__PURE__*/ function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_9__._)(function() {\n            var response, points, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_10__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        setLoading(true);\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            4,\n                            5\n                        ]);\n                        return [\n                            4,\n                            _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].getGazeStabilityDetail(recordId)\n                        ];\n                    case 2:\n                        response = _state.sent();\n                        setRecord(response.data);\n                        // 解析注视轨迹数据\n                        if (response.data.gazeTrajectoryJson) {\n                            try {\n                                points = JSON.parse(response.data.gazeTrajectoryJson);\n                                // 确保解析的数据是数组\n                                if (Array.isArray(points)) {\n                                    setGazePoints(points);\n                                } else {\n                                    console.error('注视轨迹数据不是数组格式:', points);\n                                    setGazePoints([]);\n                                }\n                            } catch (error) {\n                                console.error('解析注视轨迹数据失败:', error);\n                                setGazePoints([]);\n                            }\n                        } else {\n                            setGazePoints([]);\n                        }\n                        return [\n                            3,\n                            5\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        toast({\n                            title: \"获取详情失败\",\n                            description: error instanceof Error ? error.message : \"请检查网络连接\",\n                            variant: \"destructive\"\n                        });\n                        console.error('获取注视稳定性测试详情失败:', error);\n                        return [\n                            3,\n                            5\n                        ];\n                    case 4:\n                        setLoading(false);\n                        return [\n                            7\n                        ];\n                    case 5:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function fetchDetail() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GazeStabilityDetail.useEffect\": function() {\n            fetchDetail();\n        }\n    }[\"GazeStabilityDetail.useEffect\"], [\n        recordId\n    ]);\n    // 状态标签颜色\n    var getStatusBadge = function(status, statusDesc) {\n        var colorMap = {\n            COMPLETED: 'bg-green-100 text-green-800',\n            IN_PROGRESS: 'bg-blue-100 text-blue-800',\n            FAILED: 'bg-red-100 text-red-800',\n            PENDING: 'bg-yellow-100 text-yellow-800'\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n            className: colorMap[status] || 'bg-gray-100 text-gray-800',\n            children: statusDesc\n        }, void 0, false, {\n            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, _this);\n    };\n    // 格式化时间\n    var formatDateTime = function(dateTime) {\n        return new Date(dateTime).toLocaleString('zh-CN');\n    };\n    // 格式化持续时间\n    var formatDuration = function(duration) {\n        var seconds = Math.floor(duration / 1000);\n        var minutes = Math.floor(seconds / 60);\n        var remainingSeconds = seconds % 60;\n        return minutes > 0 ? \"\".concat(minutes, \"分\").concat(remainingSeconds, \"秒\") : \"\".concat(remainingSeconds, \"秒\");\n    };\n    // 计算统计数据\n    var getStatistics = function() {\n        // 确保gazePoints是数组且不为空\n        if (!Array.isArray(gazePoints) || gazePoints.length === 0) {\n            return null;\n        }\n        try {\n            var _Math, _Math1;\n            var totalPoints = gazePoints.length;\n            var inTargetCount = gazePoints.filter(function(p) {\n                return p && p.isInTargetArea;\n            }).length;\n            var validPoints = gazePoints.filter(function(p) {\n                return p && typeof p.relativeDistance === 'number';\n            });\n            if (validPoints.length === 0) {\n                return null;\n            }\n            var avgDistance = validPoints.reduce(function(sum, p) {\n                return sum + p.relativeDistance;\n            }, 0) / validPoints.length;\n            var distances = validPoints.map(function(p) {\n                return p.relativeDistance;\n            });\n            var maxDistance = (_Math = Math).max.apply(_Math, (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_11__._)(distances));\n            var minDistance = (_Math1 = Math).min.apply(_Math1, (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_11__._)(distances));\n            return {\n                totalPoints: totalPoints,\n                inTargetCount: inTargetCount,\n                inTargetRate: (inTargetCount / totalPoints * 100).toFixed(1),\n                avgDistance: avgDistance.toFixed(3),\n                maxDistance: maxDistance.toFixed(3),\n                minDistance: minDistance.toFixed(3)\n            };\n        } catch (error) {\n            console.error('计算统计数据时出错:', error);\n            return null;\n        }\n    };\n    var statistics = getStatistics();\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-primary\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"加载中...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n            lineNumber: 145,\n            columnNumber: 7\n        }, this);\n    }\n    if (!record) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground\",\n                    children: \"未找到测试记录\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    variant: \"outline\",\n                    onClick: onBack,\n                    className: \"mt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"h-4 w-4 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        \"返回列表\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n            lineNumber: 156,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: onBack,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"返回列表\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold tracking-tight\",\n                                        children: \"注视稳定性测试详情\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: [\n                                            \"记录ID: \",\n                                            record.recordId,\n                                            \" | 测试序列: \",\n                                            record.testSequence\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: getStatusBadge(record.status, record.statusDesc)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"患者信息\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: record.patientName\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: [\n                                                \"住院号: \",\n                                                record.inpatientNum\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: [\n                                                \"病历号: \",\n                                                record.caseCardNum\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"测试时间\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: formatDateTime(record.testDate)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"持续时间: \",\n                                                formatDuration(record.duration)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"设备信息\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: [\n                                                \"设备 \",\n                                                record.deviceId\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: [\n                                                \"SN: \",\n                                                record.deviceSn\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, this),\n                                        record.deviceName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: [\n                                                \"名称: \",\n                                                record.deviceName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                defaultValue: \"overview\",\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"overview\",\n                                children: \"概览\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"trajectory\",\n                                children: \"轨迹数据\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"analysis\",\n                                children: \"分析结果\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"settings\",\n                                children: \"测试设置\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"overview\",\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"目标点信息\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                                    children: \"X坐标\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold\",\n                                                                    children: record.targetX.toFixed(6)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                                    children: \"Y坐标\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold\",\n                                                                    children: record.targetY.toFixed(6)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                                    children: \"目标点半径\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold\",\n                                                                    children: [\n                                                                        record.targetPointRadius,\n                                                                        \"px\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 275,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                                    children: \"注视点半径\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold\",\n                                                                    children: [\n                                                                        record.gazePointRadius,\n                                                                        \"px\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, this),\n                                    statistics && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"统计信息\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                                    children: \"总点数\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold\",\n                                                                    children: statistics.totalPoints\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 298,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                                    children: \"目标区域内\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 301,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold\",\n                                                                    children: statistics.inTargetCount\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                                    children: \"命中率\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold\",\n                                                                    children: [\n                                                                        statistics.inTargetRate,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 306,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                                    children: \"平均距离\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold\",\n                                                                    children: statistics.avgDistance\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this),\n                            record.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"备注信息\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: record.notes\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"trajectory\",\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"注视轨迹可视化\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"红色圆圈为目标点，蓝色点为注视点轨迹\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-96 border rounded-lg bg-gray-50 relative overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"100%\",\n                                                height: \"100%\",\n                                                viewBox: \"0 0 800 600\",\n                                                className: \"absolute inset-0\",\n                                                children: [\n                                                    record && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                        cx: record.targetX * 800,\n                                                        cy: record.targetY * 600,\n                                                        r: record.targetPointRadius,\n                                                        fill: \"none\",\n                                                        stroke: \"red\",\n                                                        strokeWidth: \"2\",\n                                                        opacity: \"0.7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    Array.isArray(gazePoints) && gazePoints.map(function(point, index) {\n                                                        // 确保点数据有效\n                                                        if (!point || typeof point.x !== 'number' || typeof point.y !== 'number') {\n                                                            return null;\n                                                        }\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: point.x * 800,\n                                                                    cy: point.y * 600,\n                                                                    r: \"3\",\n                                                                    fill: point.isInTargetArea ? \"green\" : \"blue\",\n                                                                    opacity: \"0.6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                index > 0 && gazePoints[index - 1] && typeof gazePoints[index - 1].x === 'number' && typeof gazePoints[index - 1].y === 'number' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                    x1: gazePoints[index - 1].x * 800,\n                                                                    y1: gazePoints[index - 1].y * 600,\n                                                                    x2: point.x * 800,\n                                                                    y2: point.y * 600,\n                                                                    stroke: \"gray\",\n                                                                    strokeWidth: \"1\",\n                                                                    opacity: \"0.3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 382,\n                                                                    columnNumber: 27\n                                                                }, _this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 23\n                                                        }, _this);\n                                                    }),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                                        transform: \"translate(20, 20)\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                                x: \"0\",\n                                                                y: \"0\",\n                                                                width: \"200\",\n                                                                height: \"80\",\n                                                                fill: \"white\",\n                                                                stroke: \"gray\",\n                                                                strokeWidth: \"1\",\n                                                                opacity: \"0.9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: \"15\",\n                                                                cy: \"20\",\n                                                                r: \"3\",\n                                                                fill: \"red\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                                                                x: \"25\",\n                                                                y: \"25\",\n                                                                fontSize: \"12\",\n                                                                fill: \"black\",\n                                                                children: \"目标点\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: \"15\",\n                                                                cy: \"40\",\n                                                                r: \"3\",\n                                                                fill: \"green\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                                                                x: \"25\",\n                                                                y: \"45\",\n                                                                fontSize: \"12\",\n                                                                fill: \"black\",\n                                                                children: \"目标区域内\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: \"15\",\n                                                                cy: \"60\",\n                                                                r: \"3\",\n                                                                fill: \"blue\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                                                                x: \"25\",\n                                                                y: \"65\",\n                                                                fontSize: \"12\",\n                                                                fill: \"black\",\n                                                                children: \"目标区域外\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"注视轨迹数据\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: [\n                                                    \"共 \",\n                                                    Array.isArray(gazePoints) ? gazePoints.length : 0,\n                                                    \" 个数据点\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-h-96 overflow-y-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 gap-2\",\n                                                children: Array.isArray(gazePoints) && gazePoints.length > 0 ? gazePoints.map(function(point, index) {\n                                                    // 确保点数据有效\n                                                    if (!point) return null;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-2 border rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: [\n                                                                            \"#\",\n                                                                            point.index || index + 1\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                        lineNumber: 433,\n                                                                        columnNumber: 29\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: [\n                                                                            \"(\",\n                                                                            typeof point.x === 'number' ? point.x.toFixed(3) : 'N/A',\n                                                                            \", \",\n                                                                            typeof point.y === 'number' ? point.y.toFixed(3) : 'N/A',\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                        lineNumber: 434,\n                                                                        columnNumber: 29\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                        variant: point.isInTargetArea ? \"default\" : \"secondary\",\n                                                                        children: point.isInTargetArea ? \"目标内\" : \"目标外\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                        lineNumber: 437,\n                                                                        columnNumber: 29\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                lineNumber: 432,\n                                                                columnNumber: 27\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 text-sm text-muted-foreground\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"距离: \",\n                                                                            typeof point.relativeDistance === 'number' ? point.relativeDistance.toFixed(3) : 'N/A'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                        lineNumber: 442,\n                                                                        columnNumber: 29\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"方向: \",\n                                                                            point.direction || 'N/A'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                        lineNumber: 443,\n                                                                        columnNumber: 29\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"环级: \",\n                                                                            point.ringLevel || 'N/A'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                        lineNumber: 444,\n                                                                        columnNumber: 29\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 27\n                                                            }, _this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 25\n                                                    }, _this);\n                                                }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-muted-foreground\",\n                                                    children: \"暂无轨迹数据\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"analysis\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"分析结果\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"稳定性评分\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: record.stabilityScore !== null ? record.stabilityScore : '未计算'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"评估结果\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: record.evaluationResult || '未评估'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"X轴偏差\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: record.deviationX !== null ? record.deviationX.toFixed(6) : '未计算'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Y轴偏差\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: record.deviationY !== null ? record.deviationY.toFixed(6) : '未计算'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"注视持续时间\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: record.fixationDuration !== null ? \"\".concat(record.fixationDuration, \"ms\") : '未计算'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"注视次数\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: record.fixationCount !== null ? record.fixationCount : '未计算'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"平均距离\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: record.averageDistance !== null ? record.averageDistance.toFixed(6) : '未计算'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"最大偏差\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: record.maxDeviation !== null ? record.maxDeviation.toFixed(6) : '未计算'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"RMS误差\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: record.rmsError !== null ? record.rmsError.toFixed(6) : '未计算'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"settings\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"测试参数\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"环路半径增量\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold\",\n                                                            children: [\n                                                                record.loopRadiusIncreases,\n                                                                \"px\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"环路数量\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold\",\n                                                            children: record.loopsCount\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"校准参数\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm bg-muted p-2 rounded\",\n                                                            children: record.calibrationParams\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"环境信息\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 554,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"环境描述\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm bg-muted p-2 rounded\",\n                                                        children: record.environmentInfo\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 529,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_s(GazeStabilityDetail, \"uFx4uD9MQpxMZhwhn44pDcRBxy0=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = GazeStabilityDetail;\nvar _c;\n$RefreshReg$(_c, \"GazeStabilityDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/gaze-stability-detail.tsx\n"));

/***/ })

});