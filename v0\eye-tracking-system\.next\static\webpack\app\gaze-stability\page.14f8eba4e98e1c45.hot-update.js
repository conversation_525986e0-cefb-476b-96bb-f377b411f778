"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/gaze-stability/page",{

/***/ "(app-pages-browser)/./components/gaze-stability-page.tsx":
/*!********************************************!*\
  !*** ./components/gaze-stability-page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GazeStabilityPage)\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _gaze_stability_list__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./gaze-stability-list */ \"(app-pages-browser)/./components/gaze-stability-list.tsx\");\n/* harmony import */ var _gaze_stability_detail__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./gaze-stability-detail */ \"(app-pages-browser)/./components/gaze-stability-detail.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction GazeStabilityPage() {\n    _s();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('list'), 2), currentView = _useState[0], setCurrentView = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), selectedRecord = _useState1[0], setSelectedRecord = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), recordIdFromUrl = _useState2[0], setRecordIdFromUrl = _useState2[1];\n    var searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    var router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // 检查URL参数中是否有recordId\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GazeStabilityPage.useEffect\": function() {\n            var recordId = searchParams.get('recordId');\n            if (recordId) {\n                var id = parseInt(recordId, 10);\n                if (!isNaN(id)) {\n                    setRecordIdFromUrl(id);\n                    setCurrentView('detail');\n                }\n            }\n        }\n    }[\"GazeStabilityPage.useEffect\"], [\n        searchParams\n    ]);\n    var handleViewDetail = function(record) {\n        setSelectedRecord(record);\n        setCurrentView('detail');\n        // 更新URL，但不刷新页面\n        router.push(\"/gaze-stability?recordId=\".concat(record.recordId), {\n            scroll: false\n        });\n    };\n    var handleBackToList = function() {\n        setSelectedRecord(null);\n        setRecordIdFromUrl(null);\n        setCurrentView('list');\n        // 清除URL参数\n        router.push('/gaze-stability', {\n            scroll: false\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-6\",\n        children: currentView === 'list' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_gaze_stability_list__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            onViewDetail: handleViewDetail\n        }, void 0, false, {\n            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-page.tsx\",\n            lineNumber: 47,\n            columnNumber: 9\n        }, this) : selectedRecord && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_gaze_stability_detail__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            recordId: selectedRecord.recordId,\n            onBack: handleBackToList\n        }, void 0, false, {\n            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-page.tsx\",\n            lineNumber: 50,\n            columnNumber: 11\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-page.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_s(GazeStabilityPage, \"Hf2d/R9scqx4hhjXQJUafq5NwPY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = GazeStabilityPage;\nvar _c;\n$RefreshReg$(_c, \"GazeStabilityPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/gaze-stability-page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiRDpcXHJlc2VhcmNoLXVpXFx2MFxcZXllLXRyYWNraW5nLXN5c3RlbVxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxhcGlcXG5hdmlnYXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi4vY2xpZW50L2NvbXBvbmVudHMvbmF2aWdhdGlvbic7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW5hdmlnYXRpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ })

});