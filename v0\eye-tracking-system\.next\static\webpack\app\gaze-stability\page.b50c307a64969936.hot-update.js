"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/gaze-stability/page",{

/***/ "(app-pages-browser)/./components/gaze-stability-detail.tsx":
/*!**********************************************!*\
  !*** ./components/gaze-stability-detail.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GazeStabilityDetail)\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Calendar,Clock,Eye,FileText,Settings,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Calendar,Clock,Eye,FileText,Settings,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Calendar,Clock,Eye,FileText,Settings,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Calendar,Clock,Eye,FileText,Settings,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Calendar,Clock,Eye,FileText,Settings,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Calendar,Clock,Eye,FileText,Settings,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Calendar,Clock,Eye,FileText,Settings,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Calendar,Clock,Eye,FileText,Settings,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Calendar,Clock,Eye,FileText,Settings,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Calendar,Clock,Eye,FileText,Settings,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction GazeStabilityDetail(param) {\n    var _this = this;\n    var recordId = param.recordId, onBack = param.onBack;\n    _s();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_8__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), record = _useState[0], setRecord = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_8__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]), 2), gazePoints = _useState1[0], setGazePoints = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_8__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), loading = _useState2[0], setLoading = _useState2[1];\n    var toast = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)().toast;\n    // 获取详情数据\n    var fetchDetail = /*#__PURE__*/ function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_9__._)(function() {\n            var response, points, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_10__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        setLoading(true);\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            4,\n                            5\n                        ]);\n                        return [\n                            4,\n                            _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].getGazeStabilityDetail(recordId)\n                        ];\n                    case 2:\n                        response = _state.sent();\n                        setRecord(response.data);\n                        // 解析注视轨迹数据\n                        if (response.data.gazeTrajectoryJson) {\n                            try {\n                                points = JSON.parse(response.data.gazeTrajectoryJson);\n                                // 确保解析的数据是数组\n                                if (Array.isArray(points)) {\n                                    setGazePoints(points);\n                                } else {\n                                    console.error('注视轨迹数据不是数组格式:', points);\n                                    setGazePoints([]);\n                                }\n                            } catch (error) {\n                                console.error('解析注视轨迹数据失败:', error);\n                                setGazePoints([]);\n                            }\n                        } else {\n                            setGazePoints([]);\n                        }\n                        return [\n                            3,\n                            5\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        toast({\n                            title: \"获取详情失败\",\n                            description: error instanceof Error ? error.message : \"请检查网络连接\",\n                            variant: \"destructive\"\n                        });\n                        console.error('获取注视稳定性测试详情失败:', error);\n                        return [\n                            3,\n                            5\n                        ];\n                    case 4:\n                        setLoading(false);\n                        return [\n                            7\n                        ];\n                    case 5:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function fetchDetail() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GazeStabilityDetail.useEffect\": function() {\n            fetchDetail();\n        }\n    }[\"GazeStabilityDetail.useEffect\"], [\n        recordId\n    ]);\n    // 状态标签颜色\n    var getStatusBadge = function(status, statusDesc) {\n        var colorMap = {\n            COMPLETED: 'bg-green-100 text-green-800',\n            IN_PROGRESS: 'bg-blue-100 text-blue-800',\n            FAILED: 'bg-red-100 text-red-800',\n            PENDING: 'bg-yellow-100 text-yellow-800'\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n            className: colorMap[status] || 'bg-gray-100 text-gray-800',\n            children: statusDesc\n        }, void 0, false, {\n            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, _this);\n    };\n    // 格式化时间\n    var formatDateTime = function(dateTime) {\n        return new Date(dateTime).toLocaleString('zh-CN');\n    };\n    // 格式化持续时间\n    var formatDuration = function(duration) {\n        var seconds = Math.floor(duration / 1000);\n        var minutes = Math.floor(seconds / 60);\n        var remainingSeconds = seconds % 60;\n        return minutes > 0 ? \"\".concat(minutes, \"分\").concat(remainingSeconds, \"秒\") : \"\".concat(remainingSeconds, \"秒\");\n    };\n    // 计算统计数据\n    var getStatistics = function() {\n        // 确保gazePoints是数组且不为空\n        if (!Array.isArray(gazePoints) || gazePoints.length === 0) {\n            return null;\n        }\n        try {\n            var _Math, _Math1;\n            var totalPoints = gazePoints.length;\n            var inTargetCount = gazePoints.filter(function(p) {\n                return p && p.isInTargetArea;\n            }).length;\n            var validPoints = gazePoints.filter(function(p) {\n                return p && typeof p.relativeDistance === 'number';\n            });\n            if (validPoints.length === 0) {\n                return null;\n            }\n            var avgDistance = validPoints.reduce(function(sum, p) {\n                return sum + p.relativeDistance;\n            }, 0) / validPoints.length;\n            var distances = validPoints.map(function(p) {\n                return p.relativeDistance;\n            });\n            var maxDistance = (_Math = Math).max.apply(_Math, (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_11__._)(distances));\n            var minDistance = (_Math1 = Math).min.apply(_Math1, (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_11__._)(distances));\n            return {\n                totalPoints: totalPoints,\n                inTargetCount: inTargetCount,\n                inTargetRate: (inTargetCount / totalPoints * 100).toFixed(1),\n                avgDistance: avgDistance.toFixed(3),\n                maxDistance: maxDistance.toFixed(3),\n                minDistance: minDistance.toFixed(3)\n            };\n        } catch (error) {\n            console.error('计算统计数据时出错:', error);\n            return null;\n        }\n    };\n    var statistics = getStatistics();\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-primary\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"加载中...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n            lineNumber: 145,\n            columnNumber: 7\n        }, this);\n    }\n    if (!record) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground\",\n                    children: \"未找到测试记录\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    variant: \"outline\",\n                    onClick: onBack,\n                    className: \"mt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"h-4 w-4 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        \"返回列表\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n            lineNumber: 156,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: onBack,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"返回列表\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold tracking-tight\",\n                                        children: \"注视稳定性测试详情\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: [\n                                            \"记录ID: \",\n                                            record.recordId,\n                                            \" | 测试序列: \",\n                                            record.testSequence\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: getStatusBadge(record.status, record.statusDesc)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"患者信息\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: record.patientName\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: [\n                                                \"住院号: \",\n                                                record.inpatientNum\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: [\n                                                \"病历号: \",\n                                                record.caseCardNum\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"测试时间\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: formatDateTime(record.testDate)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"持续时间: \",\n                                                formatDuration(record.duration)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"设备信息\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: [\n                                                \"设备 \",\n                                                record.deviceId\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: [\n                                                \"SN: \",\n                                                record.deviceSn\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, this),\n                                        record.deviceName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: [\n                                                \"名称: \",\n                                                record.deviceName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                defaultValue: \"overview\",\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"overview\",\n                                children: \"概览\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"trajectory\",\n                                children: \"轨迹数据\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"analysis\",\n                                children: \"分析结果\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                value: \"settings\",\n                                children: \"测试设置\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"overview\",\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"目标点信息\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                                    children: \"X坐标\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold\",\n                                                                    children: record.targetX.toFixed(6)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                                    children: \"Y坐标\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold\",\n                                                                    children: record.targetY.toFixed(6)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                                    children: \"目标点半径\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold\",\n                                                                    children: [\n                                                                        record.targetPointRadius,\n                                                                        \"px\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 275,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                                    children: \"注视点半径\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold\",\n                                                                    children: [\n                                                                        record.gazePointRadius,\n                                                                        \"px\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, this),\n                                    statistics && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"统计信息\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                                    children: \"总点数\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold\",\n                                                                    children: statistics.totalPoints\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 298,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                                    children: \"目标区域内\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 301,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold\",\n                                                                    children: statistics.inTargetCount\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                                    children: \"命中率\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold\",\n                                                                    children: [\n                                                                        statistics.inTargetRate,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 306,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                                    children: \"平均距离\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold\",\n                                                                    children: statistics.avgDistance\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this),\n                            record.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"备注信息\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: record.notes\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"trajectory\",\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"注视轨迹可视化\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"红色圆圈为目标点，蓝色点为注视点轨迹\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-96 border rounded-lg bg-gray-50 relative overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"100%\",\n                                                height: \"100%\",\n                                                viewBox: \"0 0 800 600\",\n                                                className: \"absolute inset-0\",\n                                                children: [\n                                                    record && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                        cx: record.targetX * 800,\n                                                        cy: record.targetY * 600,\n                                                        r: record.targetPointRadius,\n                                                        fill: \"none\",\n                                                        stroke: \"red\",\n                                                        strokeWidth: \"2\",\n                                                        opacity: \"0.7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    Array.isArray(gazePoints) && gazePoints.map(function(point, index) {\n                                                        // 确保点数据有效\n                                                        if (!point || typeof point.x !== 'number' || typeof point.y !== 'number') {\n                                                            return null;\n                                                        }\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: point.x * 800,\n                                                                    cy: point.y * 600,\n                                                                    r: \"3\",\n                                                                    fill: point.isInTargetArea ? \"green\" : \"blue\",\n                                                                    opacity: \"0.6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                index > 0 && gazePoints[index - 1] && typeof gazePoints[index - 1].x === 'number' && typeof gazePoints[index - 1].y === 'number' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                    x1: gazePoints[index - 1].x * 800,\n                                                                    y1: gazePoints[index - 1].y * 600,\n                                                                    x2: point.x * 800,\n                                                                    y2: point.y * 600,\n                                                                    stroke: \"gray\",\n                                                                    strokeWidth: \"1\",\n                                                                    opacity: \"0.3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                    lineNumber: 382,\n                                                                    columnNumber: 27\n                                                                }, _this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 23\n                                                        }, _this);\n                                                    }),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                                        transform: \"translate(20, 20)\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                                x: \"0\",\n                                                                y: \"0\",\n                                                                width: \"200\",\n                                                                height: \"80\",\n                                                                fill: \"white\",\n                                                                stroke: \"gray\",\n                                                                strokeWidth: \"1\",\n                                                                opacity: \"0.9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: \"15\",\n                                                                cy: \"20\",\n                                                                r: \"3\",\n                                                                fill: \"red\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                                                                x: \"25\",\n                                                                y: \"25\",\n                                                                fontSize: \"12\",\n                                                                fill: \"black\",\n                                                                children: \"目标点\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: \"15\",\n                                                                cy: \"40\",\n                                                                r: \"3\",\n                                                                fill: \"green\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                                                                x: \"25\",\n                                                                y: \"45\",\n                                                                fontSize: \"12\",\n                                                                fill: \"black\",\n                                                                children: \"目标区域内\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: \"15\",\n                                                                cy: \"60\",\n                                                                r: \"3\",\n                                                                fill: \"blue\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                                                                x: \"25\",\n                                                                y: \"65\",\n                                                                fontSize: \"12\",\n                                                                fill: \"black\",\n                                                                children: \"目标区域外\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"注视轨迹数据\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: [\n                                                    \"共 \",\n                                                    Array.isArray(gazePoints) ? gazePoints.length : 0,\n                                                    \" 个数据点\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-h-96 overflow-y-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 gap-2\",\n                                                children: Array.isArray(gazePoints) && gazePoints.length > 0 ? gazePoints.map(function(point, index) {\n                                                    // 确保点数据有效\n                                                    if (!point) return null;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-2 border rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: [\n                                                                            \"#\",\n                                                                            point.index || index + 1\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                        lineNumber: 433,\n                                                                        columnNumber: 29\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: [\n                                                                            \"(\",\n                                                                            typeof point.x === 'number' ? point.x.toFixed(3) : 'N/A',\n                                                                            \", \",\n                                                                            typeof point.y === 'number' ? point.y.toFixed(3) : 'N/A',\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                        lineNumber: 434,\n                                                                        columnNumber: 29\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                        variant: point.isInTargetArea ? \"default\" : \"secondary\",\n                                                                        children: point.isInTargetArea ? \"目标内\" : \"目标外\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                        lineNumber: 437,\n                                                                        columnNumber: 29\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                lineNumber: 432,\n                                                                columnNumber: 27\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 text-sm text-muted-foreground\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"距离: \",\n                                                                            typeof point.relativeDistance === 'number' ? point.relativeDistance.toFixed(3) : 'N/A'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                        lineNumber: 442,\n                                                                        columnNumber: 29\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"方向: \",\n                                                                            point.direction || 'N/A'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                        lineNumber: 443,\n                                                                        columnNumber: 29\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"环级: \",\n                                                                            point.ringLevel || 'N/A'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                        lineNumber: 444,\n                                                                        columnNumber: 29\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 27\n                                                            }, _this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 25\n                                                    }, _this);\n                                                }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-muted-foreground\",\n                                                    children: \"暂无轨迹数据\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"analysis\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Calendar_Clock_Eye_FileText_Settings_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"分析结果\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"稳定性评分\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: record.stabilityScore !== null ? record.stabilityScore : '未计算'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"评估结果\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: record.evaluationResult || '未评估'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"X轴偏差\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: record.deviationX !== null ? record.deviationX.toFixed(6) : '未计算'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Y轴偏差\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: record.deviationY !== null ? record.deviationY.toFixed(6) : '未计算'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"注视持续时间\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: record.fixationDuration !== null ? \"\".concat(record.fixationDuration, \"ms\") : '未计算'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"注视次数\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: record.fixationCount !== null ? record.fixationCount : '未计算'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"平均距离\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: record.averageDistance !== null ? record.averageDistance.toFixed(6) : '未计算'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"最大偏差\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: record.maxDeviation !== null ? record.maxDeviation.toFixed(6) : '未计算'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"RMS误差\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: record.rmsError !== null ? record.rmsError.toFixed(6) : '未计算'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                        value: \"settings\",\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"测试参数\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"环路半径增量\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold\",\n                                                            children: [\n                                                                record.loopRadiusIncreases,\n                                                                \"px\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"环路数量\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold\",\n                                                            children: record.loopsCount\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"校准参数\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm bg-muted p-2 rounded\",\n                                                            children: record.calibrationParams\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"环境信息\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 554,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"环境描述\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm bg-muted p-2 rounded\",\n                                                        children: record.environmentInfo\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                        lineNumber: 529,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-detail.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_s(GazeStabilityDetail, \"uFx4uD9MQpxMZhwhn44pDcRBxy0=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = GazeStabilityDetail;\nvar _c;\n$RefreshReg$(_c, \"GazeStabilityDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/gaze-stability-detail.tsx\n"));

/***/ })

});