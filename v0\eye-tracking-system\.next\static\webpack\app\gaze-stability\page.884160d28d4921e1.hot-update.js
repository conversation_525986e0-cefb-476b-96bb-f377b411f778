"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/gaze-stability/page",{

/***/ "(app-pages-browser)/./components/gaze-stability-page.tsx":
/*!********************************************!*\
  !*** ./components/gaze-stability-page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GazeStabilityPage)\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _gaze_stability_list__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./gaze-stability-list */ \"(app-pages-browser)/./components/gaze-stability-list.tsx\");\n/* harmony import */ var _gaze_stability_detail__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./gaze-stability-detail */ \"(app-pages-browser)/./components/gaze-stability-detail.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction GazeStabilityPage() {\n    _s();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('list'), 2), currentView = _useState[0], setCurrentView = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), selectedRecord = _useState1[0], setSelectedRecord = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), recordIdFromUrl = _useState2[0], setRecordIdFromUrl = _useState2[1];\n    var searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    var router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // 检查URL参数中是否有recordId\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GazeStabilityPage.useEffect\": function() {\n            var recordId = searchParams.get('recordId');\n            if (recordId) {\n                var id = parseInt(recordId, 10);\n                if (!isNaN(id)) {\n                    setRecordIdFromUrl(id);\n                    setCurrentView('detail');\n                }\n            }\n        }\n    }[\"GazeStabilityPage.useEffect\"], [\n        searchParams\n    ]);\n    var handleViewDetail = function(record) {\n        setSelectedRecord(record);\n        setCurrentView('detail');\n        // 更新URL，但不刷新页面\n        router.push(\"/gaze-stability?recordId=\".concat(record.recordId), {\n            scroll: false\n        });\n    };\n    var handleBackToList = function() {\n        setSelectedRecord(null);\n        setRecordIdFromUrl(null);\n        setCurrentView('list');\n        // 清除URL参数\n        router.push('/gaze-stability', {\n            scroll: false\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-6\",\n        children: currentView === 'list' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_gaze_stability_list__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            onViewDetail: handleViewDetail\n        }, void 0, false, {\n            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-page.tsx\",\n            lineNumber: 47,\n            columnNumber: 9\n        }, this) : // 显示详情页面，优先使用URL中的recordId，其次使用选中的记录\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_gaze_stability_detail__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            recordId: recordIdFromUrl || (selectedRecord === null || selectedRecord === void 0 ? void 0 : selectedRecord.recordId) || 0,\n            onBack: handleBackToList\n        }, void 0, false, {\n            fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-page.tsx\",\n            lineNumber: 50,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\research-ui\\\\v0\\\\eye-tracking-system\\\\components\\\\gaze-stability-page.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_s(GazeStabilityPage, \"Hf2d/R9scqx4hhjXQJUafq5NwPY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = GazeStabilityPage;\nvar _c;\n$RefreshReg$(_c, \"GazeStabilityPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/gaze-stability-page.tsx\n"));

/***/ })

});