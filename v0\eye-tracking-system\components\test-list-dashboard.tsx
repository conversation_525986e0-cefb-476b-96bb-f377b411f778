"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Eye, Target, Move, Zap, Calendar, Clock, User, Plus, Filter, LogOut, Settings } from "lucide-react"
import Link from "next/link"

// 模拟测试数据
const generateTestData = () => {
  const testTypes = [
    { id: "fixation", name: "注视稳定性", icon: Target, color: "bg-blue-500" },
    { id: "pursuit", name: "追随能力", icon: Move, color: "bg-green-500" },
    { id: "saccade", name: "扫视能力", icon: Zap, color: "bg-purple-500" },
    { id: "aoi", name: "兴趣区域", icon: Eye, color: "bg-orange-500" },
  ]

  const patients = ["张三", "李四", "王五", "赵六", "陈七"]
  const statuses = ["completed", "processing", "failed"]
  const statusLabels = { completed: "已完成", processing: "处理中", failed: "失败" }
  const statusColors = { completed: "default", processing: "secondary", failed: "destructive" }

  return testTypes.map((type) => ({
    ...type,
    tests: Array.from({ length: 12 }, (_, i) => ({
      id: `${type.id}-${i + 1}`,
      testId: `T${String(i + 1).padStart(3, "0")}`,
      patient: patients[Math.floor(Math.random() * patients.length)],
      date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString(),
      time: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toLocaleTimeString("zh-CN", {
        hour: "2-digit",
        minute: "2-digit",
      }),
      duration: Math.floor(Math.random() * 300 + 60), // 60-360秒
      status: statuses[Math.floor(Math.random() * statuses.length)],
      score: Math.floor(Math.random() * 40 + 60), // 60-100分
      type: type.id,
      summary: {
        fixation: `稳定性: ${(Math.random() * 40 + 60).toFixed(1)}%`,
        pursuit: `精度: ${(Math.random() * 40 + 60).toFixed(1)}%`,
        saccade: `频率: ${(Math.random() * 3 + 2).toFixed(1)}/s`,
        aoi: `区域: ${Math.floor(Math.random() * 5 + 3)}个`,
      }[type.id],
    })),
  }))
}

export default function TestListDashboard() {
  const [activeTab, setActiveTab] = useState("fixation")
  const [testData] = useState(generateTestData())

  const currentTestType = testData.find((t) => t.id === activeTab)
  const statusLabels = { completed: "已完成", processing: "处理中", failed: "destructive" }
  const statusColors = { completed: "default", processing: "secondary", failed: "destructive" }

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">眼球运动评估系统</h1>
            <p className="text-gray-600 mt-1">管理和查看所有眼球运动测试记录</p>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <User className="w-4 h-4" />
              <span>
                {typeof window !== "undefined" && localStorage.getItem("eyeTrackingUser")
                  ? JSON.parse(localStorage.getItem("eyeTrackingUser")!).username
                  : "用户"}
              </span>
              <span className="text-gray-400">|</span>
              <span>
                {typeof window !== "undefined" && localStorage.getItem("eyeTrackingUser")
                  ? JSON.parse(localStorage.getItem("eyeTrackingUser")!).role
                  : "角色"}
              </span>
            </div>
            <Button
              variant="outline"
              onClick={() => {
                localStorage.removeItem("eyeTrackingUser")
                window.location.href = "/login"
              }}
              className="flex items-center gap-2"
            >
              <LogOut className="w-4 h-4" />
              退出登录
            </Button>
            <Link href="/gaze-stability">
              <Button variant="outline" className="flex items-center gap-2">
                <Target className="w-4 h-4" />
                注视稳定性测试
              </Button>
            </Link>
            <Button className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              新建测试
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Filter className="w-4 h-4" />
              筛选
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {testData.map((type) => {
            const completedTests = type.tests.filter((t) => t.status === "completed").length
            const avgScore =
              type.tests.filter((t) => t.status === "completed").reduce((sum, t) => sum + t.score, 0) /
                completedTests || 0

            return (
              <Card key={type.id}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{type.name}</CardTitle>
                  <type.icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{type.tests.length}</div>
                  <p className="text-xs text-muted-foreground">
                    完成 {completedTests} 项 • 平均分 {avgScore.toFixed(1)}
                  </p>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Test Lists */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="w-5 h-5" />
              测试记录
            </CardTitle>
            <CardDescription>点击测试记录查看详细分析结果</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                {testData.map((type) => (
                  <TabsTrigger key={type.id} value={type.id} className="flex items-center gap-2">
                    <type.icon className="w-4 h-4" />
                    {type.name}
                  </TabsTrigger>
                ))}
              </TabsList>

              <TabsContent value={activeTab} className="mt-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {currentTestType?.tests.map((test) => (
                    <Link key={test.id} href={`/test/${test.id}`}>
                      <Card className="hover:shadow-md transition-shadow cursor-pointer">
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <div className={`w-3 h-3 rounded-full ${currentTestType.color}`} />
                              <CardTitle className="text-lg">{test.testId}</CardTitle>
                            </div>
                            <Badge
                              variant={statusColors[test.status as keyof typeof statusColors] as any}
                              className="text-xs"
                            >
                              {statusLabels[test.status as keyof typeof statusLabels]}
                            </Badge>
                          </div>
                          <CardDescription className="flex items-center gap-4 text-sm">
                            <span className="flex items-center gap-1">
                              <User className="w-3 h-3" />
                              {test.patient}
                            </span>
                            <span className="flex items-center gap-1">
                              <Calendar className="w-3 h-3" />
                              {test.date}
                            </span>
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <div className="space-y-2">
                            <div className="flex justify-between items-center text-sm">
                              <span className="text-gray-600">测试时长</span>
                              <span className="flex items-center gap-1">
                                <Clock className="w-3 h-3" />
                                {Math.floor(test.duration / 60)}:{String(test.duration % 60).padStart(2, "0")}
                              </span>
                            </div>
                            <div className="flex justify-between items-center text-sm">
                              <span className="text-gray-600">评分</span>
                              <span
                                className={`font-medium ${
                                  test.score >= 80
                                    ? "text-green-600"
                                    : test.score >= 60
                                      ? "text-yellow-600"
                                      : "text-red-600"
                                }`}
                              >
                                {test.score}/100
                              </span>
                            </div>
                            <div className="flex justify-between items-center text-sm">
                              <span className="text-gray-600">关键指标</span>
                              <span className="text-blue-600">{test.summary}</span>
                            </div>
                            <div className="flex justify-between items-center text-xs text-gray-500">
                              <span>测试时间</span>
                              <span>{test.time}</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
