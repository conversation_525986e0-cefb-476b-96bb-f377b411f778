"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Eye, Target, Move, Zap, Calendar, Clock, User, Plus, Filter, LogOut, Settings, Loader2, AlertCircle } from "lucide-react"
import Link from "next/link"
import apiService, { GazeStabilityRecord } from "@/lib/api"
import { useToast } from "@/hooks/use-toast"
import GazeVisualizationPlaceholder from "./gaze-visualization-placeholder"

// 测试类型定义
const testTypes = [
  { id: "fixation", name: "注视稳定性", icon: Target, color: "bg-blue-500" },
  { id: "pursuit", name: "追随能力", icon: Move, color: "bg-green-500" },
  { id: "saccade", name: "扫视能力", icon: Zap, color: "bg-purple-500" },
  { id: "aoi", name: "兴趣区域", icon: Eye, color: "bg-orange-500" },
]

// 状态映射
const statusMapping = {
  'COMPLETED': { label: '已完成', color: 'default' as const },
  'IN_PROGRESS': { label: '进行中', color: 'secondary' as const },
  'FAILED': { label: '失败', color: 'destructive' as const },
  'PENDING': { label: '待处理', color: 'secondary' as const },
}

// 将API数据转换为显示格式
const transformGazeStabilityData = (records: GazeStabilityRecord[]) => {
  return records.map((record) => ({
    id: `fixation-${record.id}`,
    testId: `T${String(record.recordId).padStart(3, "0")}`,
    patient: record.patientName,
    date: new Date(record.testDate).toLocaleDateString('zh-CN'),
    time: new Date(record.testDate).toLocaleTimeString("zh-CN", {
      hour: "2-digit",
      minute: "2-digit",
    }),
    duration: Math.floor(record.duration / 1000), // 转换为秒
    status: record.status.toLowerCase(),
    statusLabel: statusMapping[record.status as keyof typeof statusMapping]?.label || record.statusDesc,
    statusColor: statusMapping[record.status as keyof typeof statusMapping]?.color || 'default',
    score: record.stabilityScore || Math.floor(Math.random() * 40 + 60), // 如果没有评分则使用随机值
    type: "fixation",
    summary: `目标区域: (${record.targetX.toFixed(2)}, ${record.targetY.toFixed(2)})`,
    recordId: record.recordId,
    patientId: record.patientId,
    deviceSn: record.deviceSn,
    notes: record.notes,
  }))
}

// 生成其他类型的模拟数据
const generateMockDataForOtherTypes = () => {
  const patients = ["张三", "李四", "王五", "赵六", "陈七"]
  const statuses = ["completed", "processing", "failed"]

  return testTypes.slice(1).map((type) => ({
    ...type,
    tests: Array.from({ length: 8 }, (_, i) => ({
      id: `${type.id}-${i + 1}`,
      testId: `T${String(i + 1).padStart(3, "0")}`,
      patient: patients[Math.floor(Math.random() * patients.length)],
      date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString('zh-CN'),
      time: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toLocaleTimeString("zh-CN", {
        hour: "2-digit",
        minute: "2-digit",
      }),
      duration: Math.floor(Math.random() * 300 + 60),
      status: statuses[Math.floor(Math.random() * statuses.length)],
      statusLabel: statusMapping.COMPLETED.label,
      statusColor: statusMapping.COMPLETED.color,
      score: Math.floor(Math.random() * 40 + 60),
      type: type.id,
      summary: {
        pursuit: `精度: ${(Math.random() * 40 + 60).toFixed(1)}%`,
        saccade: `频率: ${(Math.random() * 3 + 2).toFixed(1)}/s`,
        aoi: `区域: ${Math.floor(Math.random() * 5 + 3)}个`,
      }[type.id],
    })),
  }))
}

export default function TestListDashboard() {
  const [activeTab, setActiveTab] = useState("fixation")
  const [gazeStabilityData, setGazeStabilityData] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  // 获取注视稳定性数据
  const fetchGazeStabilityData = async () => {
    setLoading(true)
    setError(null)
    try {
      const response = await apiService.getGazeStabilityList({
        current: 1,
        size: 20, // 获取更多数据用于展示
      })
      const transformedData = transformGazeStabilityData(response.data.records)
      setGazeStabilityData(transformedData)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取数据失败'
      setError(errorMessage)
      toast({
        title: "获取数据失败",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // 组件挂载时获取数据
  useEffect(() => {
    fetchGazeStabilityData()
  }, [])

  // 合并真实数据和模拟数据
  const testData = [
    {
      id: "fixation",
      name: "注视稳定性",
      icon: Target,
      color: "bg-blue-500",
      tests: gazeStabilityData,
    },
    ...generateMockDataForOtherTypes(),
  ]

  const currentTestType = testData.find((t) => t.id === activeTab)
  const statusLabels = { completed: "已完成", processing: "处理中", failed: "失败" }
  const statusColors = { completed: "default", processing: "secondary", failed: "destructive" }

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">眼球运动评估系统</h1>
            <p className="text-gray-600 mt-1">管理和查看所有眼球运动测试记录</p>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <User className="w-4 h-4" />
              <span>
                {typeof window !== "undefined" && localStorage.getItem("eyeTrackingUser")
                  ? JSON.parse(localStorage.getItem("eyeTrackingUser")!).username
                  : "用户"}
              </span>
              <span className="text-gray-400">|</span>
              <span>
                {typeof window !== "undefined" && localStorage.getItem("eyeTrackingUser")
                  ? JSON.parse(localStorage.getItem("eyeTrackingUser")!).role
                  : "角色"}
              </span>
            </div>
            <Button
              variant="outline"
              onClick={() => {
                localStorage.removeItem("eyeTrackingUser")
                window.location.href = "/login"
              }}
              className="flex items-center gap-2"
            >
              <LogOut className="w-4 h-4" />
              退出登录
            </Button>
            <Link href="/gaze-stability">
              <Button variant="outline" className="flex items-center gap-2">
                <Target className="w-4 h-4" />
                注视稳定性测试
              </Button>
            </Link>
            <Button className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              新建测试
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Filter className="w-4 h-4" />
              筛选
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {testData.map((type) => {
            const completedTests = type.tests.filter((t: any) =>
              t.status === "completed" || t.status === "COMPLETED"
            ).length
            const avgScore =
              type.tests.filter((t: any) => t.status === "completed" || t.status === "COMPLETED")
                .reduce((sum: number, t: any) => sum + (t.score || 0), 0) / completedTests || 0

            return (
              <Card key={type.id}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{type.name}</CardTitle>
                  <type.icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{type.tests.length}</div>
                  <p className="text-xs text-muted-foreground">
                    完成 {completedTests} 项 • 平均分 {avgScore.toFixed(1)}
                  </p>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* 注视稳定性数据可视化 */}
        {gazeStabilityData.length > 0 && (
          <GazeVisualizationPlaceholder
            recordCount={gazeStabilityData.length}
            avgStability={gazeStabilityData.reduce((sum, record) => sum + (record.score || 0), 0) / gazeStabilityData.length}
          />
        )}

        {/* Test Lists */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="w-5 h-5" />
              测试记录
            </CardTitle>
            <CardDescription>点击测试记录查看详细分析结果</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                {testData.map((type) => (
                  <TabsTrigger key={type.id} value={type.id} className="flex items-center gap-2">
                    <type.icon className="w-4 h-4" />
                    {type.name}
                  </TabsTrigger>
                ))}
              </TabsList>

              <TabsContent value={activeTab} className="mt-6">
                {loading && activeTab === "fixation" ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin mr-2" />
                    <span>加载注视稳定性数据...</span>
                  </div>
                ) : error && activeTab === "fixation" ? (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      {error}
                      <Button
                        variant="outline"
                        size="sm"
                        className="ml-2"
                        onClick={fetchGazeStabilityData}
                      >
                        重试
                      </Button>
                    </AlertDescription>
                  </Alert>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {currentTestType?.tests.map((test: any) => (
                      <Link
                        key={test.id}
                        href={activeTab === "fixation" && test.recordId ? `/gaze-stability` : `/test/${test.id}`}
                      >
                        <Card className="hover:shadow-md transition-shadow cursor-pointer">
                          <CardHeader className="pb-3">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <div className={`w-3 h-3 rounded-full ${currentTestType.color}`} />
                                <CardTitle className="text-lg">{test.testId}</CardTitle>
                              </div>
                              <Badge
                                variant={test.statusColor || statusColors[test.status as keyof typeof statusColors] as any}
                                className="text-xs"
                              >
                                {test.statusLabel || statusLabels[test.status as keyof typeof statusLabels]}
                              </Badge>
                            </div>
                            <CardDescription className="flex items-center gap-4 text-sm">
                              <span className="flex items-center gap-1">
                                <User className="w-3 h-3" />
                                {test.patient}
                              </span>
                              <span className="flex items-center gap-1">
                                <Calendar className="w-3 h-3" />
                                {test.date}
                              </span>
                            </CardDescription>
                          </CardHeader>
                          <CardContent className="pt-0">
                            <div className="space-y-2">
                              <div className="flex justify-between items-center text-sm">
                                <span className="text-gray-600">测试时长</span>
                                <span className="flex items-center gap-1">
                                  <Clock className="w-3 h-3" />
                                  {Math.floor(test.duration / 60)}:{String(test.duration % 60).padStart(2, "0")}
                                </span>
                              </div>
                              {test.score && (
                                <div className="flex justify-between items-center text-sm">
                                  <span className="text-gray-600">评分</span>
                                  <span
                                    className={`font-medium ${
                                      test.score >= 80
                                        ? "text-green-600"
                                        : test.score >= 60
                                          ? "text-yellow-600"
                                          : "text-red-600"
                                    }`}
                                  >
                                    {test.score}/100
                                  </span>
                                </div>
                              )}
                              <div className="flex justify-between items-center text-sm">
                                <span className="text-gray-600">关键指标</span>
                                <span className="text-blue-600 text-xs">{test.summary}</span>
                              </div>
                              {activeTab === "fixation" && test.deviceSn && (
                                <div className="flex justify-between items-center text-xs text-gray-500">
                                  <span>设备SN</span>
                                  <span>{test.deviceSn}</span>
                                </div>
                              )}
                              <div className="flex justify-between items-center text-xs text-gray-500">
                                <span>测试时间</span>
                                <span>{test.time}</span>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </Link>
                    ))}
                    {currentTestType?.tests.length === 0 && !loading && (
                      <div className="col-span-full text-center py-8 text-gray-500">
                        暂无{currentTestType.name}测试数据
                      </div>
                    )}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
